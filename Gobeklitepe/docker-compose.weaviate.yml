version: '3.8'

services:
  # Weaviate Core Database
  weaviate:
    image: semitechnologies/weaviate:1.25.0
    container_name: hvac-weaviate
    ports:
      - "8082:8080"  # Changed to avoid conflicts
      - "50051:50051"  # gRPC port
    environment:
      # Basic Configuration
      QUERY_DEFAULTS_LIMIT: 25
      AUTHENTICATION_ANONYMOUS_ACCESS_ENABLED: 'true'
      PERSISTENCE_DATA_PATH: '/var/lib/weaviate'
      
      # Vectorizer Configuration
      DEFAULT_VECTORIZER_MODULE: 'text2vec-transformers'
      ENABLE_MODULES: 'text2vec-transformers,backup-filesystem'
      TRANSFORMERS_INFERENCE_API: 'http://t2v-transformers:8080'
      
      # Resource Management (Optimized for WSL with 40GB RAM, 4 cores max)
      LIMIT_RESOURCES: 'true'
      GOMEMLIMIT: '12884901888'  # 12GB in bytes (conservative for 40GB total)
      GOMAXPROCS: '4'     # Use 4 cores as specified
      
      # Performance Optimization
      QUERY_MAXIMUM_RESULTS: 10000
      TRACK_VECTOR_DIMENSIONS: 'true'
      
      # Cluster Configuration
      CLUSTER_HOSTNAME: 'node1'
      CLUSTER_GOSSIP_BIND_PORT: '7100'
      CLUSTER_DATA_BIND_PORT: '7101'
      
      # Backup Configuration
      BACKUP_FILESYSTEM_PATH: '/var/lib/weaviate/backups'
      
      # Monitoring
      PROMETHEUS_MONITORING_ENABLED: 'true'
      PROMETHEUS_MONITORING_PORT: '2112'
      
    volumes:
      - ./weaviate_data:/var/lib/weaviate
      - ./weaviate_backups:/var/lib/weaviate/backups
    restart: on-failure:3
    depends_on:
      - t2v-transformers
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:8080/v1/.well-known/ready"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    deploy:
      resources:
        limits:
          memory: 14GB  # Hard limit slightly above GOMEMLIMIT (12GB)
          cpus: '4.0'   # Limit to 4 CPU cores
        reservations:
          memory: 10GB
          cpus: '2.0'
    networks:
      - weaviate-network

  # Local Embedding Model (CPU-only for compatibility)
  t2v-transformers:
    image: cr.weaviate.io/semitechnologies/transformers-inference:sentence-transformers-all-mpnet-base-v2
    container_name: hvac-embeddings
    environment:
      ENABLE_CUDA: '0'  # Disable GPU for compatibility

      # Model Configuration
      MODEL_NAME: 'sentence-transformers/all-mpnet-base-v2'
      MAX_SEQUENCE_LENGTH: '512'
      BATCH_SIZE: '8'  # Reduced batch size for CPU

      # Performance Tuning for CPU
      WORKERS: '2'  # Reduced workers for CPU
      TIMEOUT: '120'  # Increased timeout for CPU processing
    restart: on-failure:3
    healthcheck:
      test: ["CMD", "python3", "-c", "import urllib.request; urllib.request.urlopen('http://localhost:8080/.well-known/ready')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 180s  # Longer startup for CPU model loading
    networks:
      - weaviate-network



  # Monitoring and Metrics
  prometheus:
    image: prom/prometheus:latest
    container_name: hvac-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=30d'
      - '--web.enable-lifecycle'
    restart: unless-stopped
    profiles:
      - monitoring
    networks:
      - weaviate-network

  # Metrics Visualization
  grafana:
    image: grafana/grafana:latest
    container_name: hvac-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    restart: unless-stopped
    profiles:
      - monitoring
    networks:
      - weaviate-network

  # Redis Configuration - Using External Redis Server
  # Redis service removed - using external Redis at **************:3037
  # Configure applications to connect to: **************:3037

  # Backup Service
  backup-service:
    image: alpine:latest
    container_name: hvac-backup
    volumes:
      - ./weaviate_data:/source:ro
      - ./backups:/backup
      - ./scripts/backup.sh:/backup.sh:ro
    command: |
      sh -c "
        apk add --no-cache curl &&
        chmod +x /backup.sh &&
        crond -f
      "
    environment:
      - BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM
      - WEAVIATE_URL=http://weaviate:8080
      - BACKUP_RETENTION_DAYS=30
    restart: unless-stopped
    profiles:
      - backup
    networks:
      - weaviate-network

volumes:
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  weaviate-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
